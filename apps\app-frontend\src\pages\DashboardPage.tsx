import React from 'react';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import {
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { invoicesApi, actionItemsApi } from '../services/api';
import { useTenant } from '../contexts/TenantContext';

export default function DashboardPage() {
  const { currentTenant } = useTenant();

  const { data: invoices = [] } = useQuery(
    ['invoices', currentTenant?.id],
    () => invoicesApi.getInvoices({ limit: 100 }),
    { enabled: !!currentTenant }
  );

  const { data: actionItems = [] } = useQuery(
    ['actionItems', currentTenant?.id],
    () => actionItemsApi.getActionItems({ completed: false }),
    { enabled: !!currentTenant }
  );

  // Calculate statistics
  const totalInvoices = invoices.length;
  const completedInvoices = invoices.filter(inv => inv.status === 'completed').length;
  const pendingInvoices = invoices.filter(inv => inv.status === 'pending' || inv.status === 'processing').length;
  const failedInvoices = invoices.filter(inv => inv.status === 'failed').length;
  const needsReviewInvoices = invoices.filter(inv => inv.status === 'needs_review').length;

  const urgentActionItems = actionItems.filter(item => item.priority === 'urgent').length;
  const highPriorityActionItems = actionItems.filter(item => item.priority === 'high').length;

  const stats = [
    {
      name: 'Total Invoices',
      value: totalInvoices,
      icon: DocumentTextIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'Completed',
      value: completedInvoices,
      icon: CheckCircleIcon,
      color: 'bg-green-500',
    },
    {
      name: 'Processing',
      value: pendingInvoices,
      icon: ClockIcon,
      color: 'bg-yellow-500',
    },
    {
      name: 'Needs Review',
      value: needsReviewInvoices,
      icon: ExclamationTriangleIcon,
      color: 'bg-orange-500',
    },
    {
      name: 'Failed',
      value: failedInvoices,
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
    },
    {
      name: 'Action Items',
      value: actionItems.length,
      icon: ClipboardDocumentListIcon,
      color: 'bg-purple-500',
    },
  ];

  const recentInvoices = invoices
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5);

  const priorityActionItems = actionItems
    .filter(item => item.priority === 'urgent' || item.priority === 'high')
    .sort((a, b) => {
      const priorityOrder = { urgent: 1, high: 2, medium: 3, low: 4 };
      return priorityOrder[a.priority as keyof typeof priorityOrder] - priorityOrder[b.priority as keyof typeof priorityOrder];
    })
    .slice(0, 5);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'needs_review':
        return 'bg-orange-100 text-orange-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!currentTenant) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a tenant to view the dashboard.</p>
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
          <p className="mt-2 text-sm text-gray-700">
            Overview of invoice processing and action items for {currentTenant.name}
          </p>
        </div>
      </div>

      {/* Statistics */}
      <div className="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {stats.map((stat) => (
          <div key={stat.name} className="relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:px-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${stat.color} rounded-md p-3`}>
                  <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="truncate text-sm font-medium text-gray-500">{stat.name}</dt>
                  <dd className="text-lg font-medium text-gray-900">{stat.value}</dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Alerts */}
      {(urgentActionItems > 0 || highPriorityActionItems > 0) && (
        <div className="mt-8">
          <div className="rounded-md bg-yellow-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" aria-hidden="true" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Attention Required</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>
                    You have {urgentActionItems} urgent and {highPriorityActionItems} high priority action items that need attention.
                  </p>
                </div>
                <div className="mt-4">
                  <div className="-mx-2 -my-1.5 flex">
                    <Link
                      to="/action-items"
                      className="rounded-md bg-yellow-50 px-2 py-1.5 text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-yellow-600 focus:ring-offset-2 focus:ring-offset-yellow-50"
                    >
                      View Action Items
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Recent Invoices */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Recent Invoices</h3>
              <Link
                to="/invoices"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                View all
              </Link>
            </div>
            <div className="mt-6 flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentInvoices.map((invoice) => (
                  <li key={invoice.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {invoice.supplier_name}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {invoice.invoice_number || 'No invoice number'}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                          {invoice.status}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
                {recentInvoices.length === 0 && (
                  <li className="py-4">
                    <p className="text-sm text-gray-500 text-center">No invoices yet</p>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>

        {/* Priority Action Items */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Priority Action Items</h3>
              <Link
                to="/action-items"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                View all
              </Link>
            </div>
            <div className="mt-6 flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {priorityActionItems.map((item) => (
                  <li key={item.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {item.title}
                        </p>
                        <p className="text-sm text-gray-500 truncate">
                          {item.category}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(item.priority)}`}>
                          {item.priority}
                        </span>
                      </div>
                    </div>
                  </li>
                ))}
                {priorityActionItems.length === 0 && (
                  <li className="py-4">
                    <p className="text-sm text-gray-500 text-center">No priority action items</p>
                  </li>
                )}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
