import React, { useState } from 'react';
import { useQuery, useMutation } from 'react-query';
import { ShieldCheckIcon, KeyIcon } from '@heroicons/react/24/outline';
import QRCode from 'qrcode.react';
import { authApi } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { useTenant } from '../contexts/TenantContext';
import toast from 'react-hot-toast';

export default function SettingsPage() {
  const { user, refreshUser } = useAuth();
  const { currentTenant } = useTenant();
  const [activeTab, setActiveTab] = useState('security');

  if (!currentTenant) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Please select a tenant to view settings.</p>
      </div>
    );
  }

  const tabs = [
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'account', name: 'Account', icon: KeyIcon },
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Settings</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage your account and security settings
          </p>
        </div>
      </div>

      <div className="mt-8">
        <div className="sm:hidden">
          <select
            value={activeTab}
            onChange={(e) => setActiveTab(e.target.value)}
            className="block w-full rounded-md border-gray-300 focus:border-primary-500 focus:ring-primary-500"
          >
            {tabs.map((tab) => (
              <option key={tab.id} value={tab.id}>
                {tab.name}
              </option>
            ))}
          </select>
        </div>
        <div className="hidden sm:block">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <tab.icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      <div className="mt-8">
        {activeTab === 'security' && <SecuritySettings user={user} onUserUpdate={refreshUser} />}
        {activeTab === 'account' && <AccountSettings user={user} tenant={currentTenant} />}
      </div>
    </div>
  );
}

function SecuritySettings({ user, onUserUpdate }: { user: any; onUserUpdate: () => void }) {
  const [showSetup, setShowSetup] = useState(false);
  const [setupData, setSetupData] = useState<any>(null);
  const [verificationCode, setVerificationCode] = useState('');

  const setupMutation = useMutation(authApi.setupTwoFA, {
    onSuccess: (data) => {
      setSetupData(data);
      setShowSetup(true);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.detail || 'Failed to setup 2FA');
    },
  });

  const enableMutation = useMutation(
    (code: string) => authApi.enableTwoFA(code),
    {
      onSuccess: () => {
        setShowSetup(false);
        setSetupData(null);
        setVerificationCode('');
        onUserUpdate();
        toast.success('Two-factor authentication enabled!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to enable 2FA');
      },
    }
  );

  const disableMutation = useMutation(
    ({ password, code }: { password: string; code: string }) =>
      authApi.disableTwoFA(password, code),
    {
      onSuccess: () => {
        onUserUpdate();
        toast.success('Two-factor authentication disabled!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to disable 2FA');
      },
    }
  );

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Two-Factor Authentication
          </h3>
          
          {user?.is_2fa_enabled ? (
            <div>
              <div className="flex items-center mb-4">
                <ShieldCheckIcon className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-sm text-green-700">
                  Two-factor authentication is enabled
                </span>
              </div>
              <DisableTwoFAForm onSubmit={disableMutation.mutate} isLoading={disableMutation.isLoading} />
            </div>
          ) : (
            <div>
              <p className="text-sm text-gray-600 mb-4">
                Add an extra layer of security to your account by enabling two-factor authentication.
              </p>
              <button
                onClick={() => setupMutation.mutate()}
                disabled={setupMutation.isLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {setupMutation.isLoading ? 'Setting up...' : 'Enable 2FA'}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 2FA Setup Modal */}
      {showSetup && setupData && (
        <TwoFASetupModal
          setupData={setupData}
          verificationCode={verificationCode}
          setVerificationCode={setVerificationCode}
          onEnable={() => enableMutation.mutate(verificationCode)}
          onCancel={() => {
            setShowSetup(false);
            setSetupData(null);
            setVerificationCode('');
          }}
          isLoading={enableMutation.isLoading}
        />
      )}
    </div>
  );
}

function DisableTwoFAForm({ onSubmit, isLoading }: { onSubmit: (data: any) => void; isLoading: boolean }) {
  const [password, setPassword] = useState('');
  const [code, setCode] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ password, code });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700">
          Current Password
        </label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
        />
      </div>
      <div>
        <label htmlFor="code" className="block text-sm font-medium text-gray-700">
          2FA Code
        </label>
        <input
          type="text"
          id="code"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          maxLength={6}
          required
          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          placeholder="000000"
        />
      </div>
      <button
        type="submit"
        disabled={isLoading}
        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
      >
        {isLoading ? 'Disabling...' : 'Disable 2FA'}
      </button>
    </form>
  );
}

function TwoFASetupModal({ setupData, verificationCode, setVerificationCode, onEnable, onCancel, isLoading }: any) {
  return (
    <div className="fixed inset-0 z-10 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onCancel} />

        <div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
          <div>
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary-100">
              <ShieldCheckIcon className="h-6 w-6 text-primary-600" />
            </div>
            <div className="mt-3 text-center sm:mt-5">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                Setup Two-Factor Authentication
              </h3>
              <div className="mt-4">
                <p className="text-sm text-gray-500 mb-4">
                  Scan this QR code with your authenticator app:
                </p>
                <div className="flex justify-center mb-4">
                  <QRCode value={setupData.qr_code} size={200} />
                </div>
                <p className="text-sm text-gray-500 mb-4">
                  Or enter this secret manually: <code className="bg-gray-100 px-2 py-1 rounded">{setupData.secret}</code>
                </p>
                <div className="mb-4">
                  <label htmlFor="verification" className="block text-sm font-medium text-gray-700 mb-2">
                    Enter verification code:
                  </label>
                  <input
                    type="text"
                    id="verification"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    maxLength={6}
                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm text-center"
                    placeholder="000000"
                  />
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                  <p className="text-sm text-yellow-800 font-medium mb-2">Recovery Codes</p>
                  <p className="text-xs text-yellow-700 mb-2">
                    Save these recovery codes in a safe place. You can use them to access your account if you lose your authenticator device.
                  </p>
                  <div className="grid grid-cols-2 gap-2 text-xs font-mono">
                    {setupData.backup_codes.map((code: string, index: number) => (
                      <div key={index} className="bg-white p-1 rounded border">
                        {code}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
            <button
              type="button"
              onClick={onEnable}
              disabled={!verificationCode || verificationCode.length !== 6 || isLoading}
              className="inline-flex w-full justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed sm:col-start-2 sm:text-sm"
            >
              {isLoading ? 'Enabling...' : 'Enable 2FA'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

function AccountSettings({ user, tenant }: { user: any; tenant: any }) {
  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt className="text-sm font-medium text-gray-500">Email</dt>
              <dd className="mt-1 text-sm text-gray-900">{user?.email}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Current Tenant</dt>
              <dd className="mt-1 text-sm text-gray-900">{tenant?.name}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Role</dt>
              <dd className="mt-1 text-sm text-gray-900">{tenant?.role}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">2FA Status</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {user?.is_2fa_enabled ? (
                  <span className="text-green-600">Enabled</span>
                ) : (
                  <span className="text-red-600">Disabled</span>
                )}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Permissions</h3>
          <div className="grid grid-cols-2 gap-2">
            {tenant?.permissions?.map((permission: string) => (
              <span
                key={permission}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
              >
                {permission}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
