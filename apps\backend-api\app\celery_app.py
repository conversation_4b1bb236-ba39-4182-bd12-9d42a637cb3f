from celery import Celery
from celery.schedules import crontab
from app.config import settings

# Create Celery app
celery_app = Celery(
    "aggie",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=[
        "app.tasks.invoice_processing",
        "app.tasks.erp_integration",
        "app.tasks.notifications"
    ]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    broker_connection_retry_on_startup=True,
)

# Scheduled tasks
celery_app.conf.beat_schedule = {
    'fetch-invoices-from-erp': {
        'task': 'app.tasks.erp_integration.fetch_invoices_from_erp_task',
        'schedule': crontab(minute=0, hour='*/2'),  # Every 2 hours
    },
    'cleanup-old-temp-files': {
        'task': 'app.tasks.invoice_processing.cleanup_temp_files_task',
        'schedule': crontab(minute=0, hour=2),  # Daily at 2 AM
    },
}

if __name__ == "__main__":
    celery_app.start()
