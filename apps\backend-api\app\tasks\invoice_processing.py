from celery import current_task
from sqlalchemy.orm import Session
import logging
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any

from app.celery_app import celery_app
from app.database import SessionLocal, set_tenant_context
from app.models.invoice import Invoice, AccountingEntry
from app.models.action_item import ActionItem
from app.services.ocr_service import get_ocr_service
from app.services.llm_provider import get_provider_instance
from app.services.vector_service import get_vector_service
from app.tasks.notifications import send_action_item_notification_task

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def process_invoice_task(self, invoice_id: str):
    """Main task for processing an uploaded invoice"""
    db = SessionLocal()
    try:
        # Get invoice
        invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
        if not invoice:
            logger.error(f"Invoice {invoice_id} not found")
            return {"error": "Invoice not found"}
        
        # Set tenant context for RLS
        set_tenant_context(db, str(invoice.tenant_id))
        
        # Update status
        invoice.status = "processing"
        db.commit()
        
        # Update task progress
        self.update_state(state='PROGRESS', meta={'step': 'extracting_text', 'progress': 25})
        
        # Step 1: Extract text from file
        extracted_text = extract_text_from_invoice(invoice)
        if not extracted_text:
            raise Exception("Failed to extract text from invoice")
        
        invoice.extracted_text = extracted_text
        db.commit()
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'extracting_context', 'progress': 50})
        
        # Step 2: Extract structured context using LLM
        context = extract_context_from_text(extracted_text)
        invoice.extracted_context = context
        db.commit()
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'generating_suggestions', 'progress': 75})
        
        # Step 3: Generate accounting suggestions using RAG
        suggestions = generate_accounting_suggestions(db, invoice, context)

        # Step 4: Store suggestions and validate
        confidence_threshold = 0.8  # Configurable threshold
        needs_review = store_accounting_suggestions(db, invoice, suggestions, confidence_threshold)
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'finalizing', 'progress': 90})
        
        # Step 5: Create vector embedding for future RAG
        vector_service = get_vector_service()
        # Note: This would need to be run in an async context in production
        # For now, we'll skip the vector embedding in the sync task
        # vector_service.store_invoice_embedding(db, str(invoice.id), str(invoice.tenant_id), context)
        
        # Step 6: Determine final status and create action items if needed
        if needs_review:
            invoice.status = "needs_review"
            create_review_action_item(db, invoice)
        else:
            invoice.status = "completed"
        
        db.commit()
        
        return {
            "status": "success",
            "invoice_id": invoice_id,
            "final_status": invoice.status,
            "needs_review": needs_review
        }
        
    except Exception as e:
        logger.error(f"Error processing invoice {invoice_id}: {e}")
        
        # Update invoice with error
        if 'invoice' in locals():
            invoice.status = "failed"
            invoice.processing_error = str(e)
            db.commit()
        
        # Create error action item
        if 'invoice' in locals():
            create_error_action_item(db, invoice, str(e))
        
        db.rollback()
        raise
    finally:
        db.close()


def extract_text_from_invoice(invoice: Invoice) -> str:
    """Extract text from invoice file using OCR service"""
    try:
        ocr_service = get_ocr_service()
        text = ocr_service.extract_text_from_file(invoice.file_path, invoice.file_type)
        
        # Validate extracted text
        if not ocr_service.validate_extracted_text(text):
            raise Exception("Extracted text does not appear to be a valid invoice")
        
        logger.info(f"Extracted {len(text)} characters from invoice {invoice.id}")
        return text
        
    except Exception as e:
        logger.error(f"Error extracting text from invoice {invoice.id}: {e}")
        raise


def extract_context_from_text(text: str) -> str:
    """Extract structured context from raw text using LLM"""
    try:
        llm_provider = get_provider_instance()
        # Note: In production, this would need proper async handling
        # For now, we'll return a placeholder
        context = f"Extracted context from: {text[:100]}..."
        
        logger.info(f"Extracted context: {len(context)} characters")
        return context
        
    except Exception as e:
        logger.error(f"Error extracting context: {e}")
        raise


def generate_accounting_suggestions(db: Session, invoice: Invoice, context: str) -> Dict[str, Any]:
    """Generate accounting suggestions using RAG"""
    try:
        # For now, return mock suggestions
        # In production, this would use the LLM provider
        suggestions = {
            "entries": [
                {
                    "account_code": "4010",
                    "account_name": "Office Supplies",
                    "debit_amount": invoice.total_amount or 1000.0,
                    "credit_amount": None,
                    "description": f"Invoice from {invoice.supplier_name}",
                    "confidence_score": 0.85
                }
            ],
            "overall_confidence": 0.85,
            "reasoning": "Mock accounting suggestion"
        }
        
        logger.info(f"Generated {len(suggestions.get('entries', []))} accounting suggestions")
        return suggestions
        
    except Exception as e:
        logger.error(f"Error generating accounting suggestions: {e}")
        raise


def store_accounting_suggestions(
    db: Session,
    invoice: Invoice,
    suggestions: Dict[str, Any],
    confidence_threshold: float
) -> bool:
    """Store accounting suggestions and determine if review is needed"""
    try:
        needs_review = False
        overall_confidence = suggestions.get('overall_confidence', 0.0)
        
        # Check if overall confidence is below threshold
        if overall_confidence < confidence_threshold:
            needs_review = True
        
        # Store individual entries
        for entry_data in suggestions.get('entries', []):
            accounting_entry = AccountingEntry(
                tenant_id=invoice.tenant_id,
                invoice_id=invoice.id,
                account_code=entry_data.get('account_code', ''),
                account_name=entry_data.get('account_name', ''),
                debit_amount=entry_data.get('debit_amount'),
                credit_amount=entry_data.get('credit_amount'),
                description=entry_data.get('description', ''),
                confidence_score=entry_data.get('confidence_score', 0.0),
                is_validated=False,
                entry_data=entry_data
            )
            
            # Check individual entry confidence
            if accounting_entry.confidence_score < confidence_threshold:
                needs_review = True
            
            db.add(accounting_entry)
        
        db.commit()
        
        logger.info(f"Stored accounting suggestions for invoice {invoice.id}, needs_review: {needs_review}")
        return needs_review
        
    except Exception as e:
        logger.error(f"Error storing accounting suggestions: {e}")
        db.rollback()
        raise


def create_review_action_item(db: Session, invoice: Invoice):
    """Create action item for human review"""
    try:
        # Find a user with accounting permissions in this tenant
        # For now, we'll assign to the first admin user in the tenant
        from app.models.user import TenantUser, Role
        
        admin_tenant_user = db.query(TenantUser).join(Role).filter(
            TenantUser.tenant_id == invoice.tenant_id,
            TenantUser.is_active == True,
            Role.name.in_(['admin', 'manager', 'accountant'])
        ).first()
        
        if not admin_tenant_user:
            logger.warning(f"No suitable user found for review assignment in tenant {invoice.tenant_id}")
            return
        
        action_item = ActionItem(
            tenant_id=invoice.tenant_id,
            user_id=admin_tenant_user.user_id,
            invoice_id=invoice.id,
            title=f"Review invoice from {invoice.supplier_name}",
            description=f"AI processing completed with low confidence. Please review and validate the accounting suggestions for invoice {invoice.invoice_number or 'N/A'}.",
            priority="medium",
            category="review",
            is_completed=False
        )
        
        db.add(action_item)
        db.commit()
        
        # Send notification
        send_action_item_notification_task.delay(str(action_item.id))
        
        logger.info(f"Created review action item for invoice {invoice.id}")
        
    except Exception as e:
        logger.error(f"Error creating review action item: {e}")
        raise


def create_error_action_item(db: Session, invoice: Invoice, error_message: str):
    """Create action item for processing error"""
    try:
        from app.models.user import TenantUser, Role
        
        admin_tenant_user = db.query(TenantUser).join(Role).filter(
            TenantUser.tenant_id == invoice.tenant_id,
            TenantUser.is_active == True,
            Role.name.in_(['admin', 'manager'])
        ).first()
        
        if not admin_tenant_user:
            logger.warning(f"No admin user found for error assignment in tenant {invoice.tenant_id}")
            return
        
        action_item = ActionItem(
            tenant_id=invoice.tenant_id,
            user_id=admin_tenant_user.user_id,
            invoice_id=invoice.id,
            title=f"Invoice processing failed: {invoice.supplier_name}",
            description=f"Automatic processing failed for invoice {invoice.original_filename}. Error: {error_message}",
            priority="high",
            category="error",
            is_completed=False
        )
        
        db.add(action_item)
        db.commit()
        
        # Send notification
        send_action_item_notification_task.delay(str(action_item.id))
        
        logger.info(f"Created error action item for invoice {invoice.id}")
        
    except Exception as e:
        logger.error(f"Error creating error action item: {e}")


@celery_app.task
def cleanup_temp_files_task():
    """Clean up old temporary files"""
    try:
        from app.config import settings
        
        # Clean up files older than 30 days
        cutoff_date = datetime.now() - timedelta(days=30)
        
        upload_dir = settings.upload_dir
        if not os.path.exists(upload_dir):
            return {"message": "Upload directory does not exist"}
        
        cleaned_count = 0
        for root, dirs, files in os.walk(upload_dir):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_mtime < cutoff_date:
                        os.remove(file_path)
                        cleaned_count += 1
                except Exception as e:
                    logger.warning(f"Could not clean up file {file_path}: {e}")
        
        logger.info(f"Cleaned up {cleaned_count} old files")
        return {"cleaned_files": cleaned_count}
        
    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        raise
