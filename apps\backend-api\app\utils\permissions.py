from enum import Enum
from typing import List, Set


class Permission(str, Enum):
    """Enumeration of all system permissions"""
    # Invoice permissions
    INVOICES_READ = "invoices:read"
    INVOICES_WRITE = "invoices:write"
    INVOICES_DELETE = "invoices:delete"
    
    # User management permissions
    USERS_READ = "users:read"
    USERS_WRITE = "users:write"
    USERS_DELETE = "users:delete"
    
    # Accounting permissions
    ACCOUNTING_READ = "accounting:read"
    ACCOUNTING_WRITE = "accounting:write"
    ACCOUNTING_VALIDATE = "accounting:validate"
    
    # Settings permissions
    SETTINGS_READ = "settings:read"
    SETTINGS_WRITE = "settings:write"
    
    # Reports permissions
    REPORTS_READ = "reports:read"
    REPORTS_WRITE = "reports:write"
    
    # Action items permissions
    ACTION_ITEMS_READ = "action_items:read"
    ACTION_ITEMS_WRITE = "action_items:write"
    ACTION_ITEMS_ASSIGN = "action_items:assign"


def check_permission(user_permissions: List[str], required_permission: Permission) -> bool:
    """Check if user has required permission"""
    return required_permission.value in user_permissions


def check_permissions(user_permissions: List[str], required_permissions: List[Permission]) -> bool:
    """Check if user has all required permissions"""
    user_perms_set = set(user_permissions)
    required_perms_set = {perm.value for perm in required_permissions}
    return required_perms_set.issubset(user_perms_set)


def get_role_permissions(role_name: str) -> List[str]:
    """Get default permissions for a role"""
    role_permissions = {
        "admin": [
            Permission.INVOICES_READ,
            Permission.INVOICES_WRITE,
            Permission.INVOICES_DELETE,
            Permission.USERS_READ,
            Permission.USERS_WRITE,
            Permission.USERS_DELETE,
            Permission.ACCOUNTING_READ,
            Permission.ACCOUNTING_WRITE,
            Permission.ACCOUNTING_VALIDATE,
            Permission.SETTINGS_READ,
            Permission.SETTINGS_WRITE,
            Permission.REPORTS_READ,
            Permission.REPORTS_WRITE,
            Permission.ACTION_ITEMS_READ,
            Permission.ACTION_ITEMS_WRITE,
            Permission.ACTION_ITEMS_ASSIGN,
        ],
        "manager": [
            Permission.INVOICES_READ,
            Permission.INVOICES_WRITE,
            Permission.USERS_READ,
            Permission.USERS_WRITE,
            Permission.ACCOUNTING_READ,
            Permission.ACCOUNTING_VALIDATE,
            Permission.REPORTS_READ,
            Permission.REPORTS_WRITE,
            Permission.ACTION_ITEMS_READ,
            Permission.ACTION_ITEMS_WRITE,
            Permission.ACTION_ITEMS_ASSIGN,
        ],
        "accountant": [
            Permission.INVOICES_READ,
            Permission.INVOICES_WRITE,
            Permission.ACCOUNTING_READ,
            Permission.ACCOUNTING_WRITE,
            Permission.ACCOUNTING_VALIDATE,
            Permission.REPORTS_READ,
            Permission.ACTION_ITEMS_READ,
            Permission.ACTION_ITEMS_WRITE,
        ],
        "viewer": [
            Permission.INVOICES_READ,
            Permission.ACCOUNTING_READ,
            Permission.REPORTS_READ,
            Permission.ACTION_ITEMS_READ,
        ]
    }
    
    return [perm.value for perm in role_permissions.get(role_name, [])]
