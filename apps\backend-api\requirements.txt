# Core FastAPI and web server
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
sqlalchemy-utils==0.41.1
alembic==1.12.1
psycopg2-binary==2.9.9
pgvector==0.2.4

# Background tasks
redis==5.0.1
celery==5.3.4

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==42.0.8
pyotp==2.9.0

# Data validation and settings
pydantic[email]==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0
python-dotenv==1.0.0
sqlalchemy-utils==0.41.1

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# AI and ML
openai==1.3.7

# Image and PDF processing
pytesseract==0.3.10
pdf2image==1.16.3
pillow==10.1.0
PyPDF2==3.0.1

# QR codes for 2FA
qrcode[pil]==7.4.2

# HTTP client and OAuth
httpx==0.25.2
requests==2.31.0
aiohttp==3.9.1
authlib==1.2.1

# Encryption
cryptography==41.0.8

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0
factory-boy==3.3.0
